<div x-data="mobileReports()" class="min-h-screen bg-gray-50">
  <!-- Mobile Header -->
  <div class="<%= mobile_header_classes %>">
    <div class="flex items-center justify-between">
      <div class="flex items-center">
        <% if show_back_button %>
          <%= link_to back_url || 'javascript:history.back()',
              class: "mr-3 p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-zeiss-500" do %>
            <%= icon name: "arrow-left", class: "h-5 w-5" %>
          <% end %>
        <% end %>
        <h1 class="text-lg font-semibold text-gray-900 truncate"><%= title %></h1>
      </div>

      <div class="flex items-center space-x-2">
        <!-- Mobile Actions Dropdown -->
        <% if actions.any? %>
          <div class="relative" x-data="{ open: false }">
            <button
              @click="open = !open"
              type="button"
              class="p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-zeiss-500"
            >
              <%= icon name: "more-vertical", class: "h-5 w-5" %>
            </button>

            <div
              x-show="open"
              @click.away="open = false"
              x-transition:enter="transition ease-out duration-100"
              x-transition:enter-start="transform opacity-0 scale-95"
              x-transition:enter-end="transform opacity-100 scale-100"
              x-transition:leave="transition ease-in duration-75"
              x-transition:leave-start="transform opacity-100 scale-100"
              x-transition:leave-end="transform opacity-0 scale-95"
              class="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50"
            >
              <div class="py-1">
                <% actions.each do |action| %>
                  <%= link_to action[:url],
                      class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",
                      method: action[:method] || :get do %>
                    <% if action[:icon] %>
                      <%= icon name: action[:icon], class: "h-4 w-4 mr-2 inline" %>
                    <% end %>
                    <%= action[:label] %>
                  <% end %>
                <% end %>
              </div>
            </div>
          </div>
        <% end %>

        <!-- Mobile Menu Toggle -->
        <button
          @click="toggleMobileMenu()"
          type="button"
          class="p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-zeiss-500"
        >
          <%= icon name: "bars", class: "h-5 w-5" %>
        </button>
      </div>
    </div>

    <!-- Mobile Tabs -->
    <% if tabs.any? %>
      <div class="mt-3 border-t border-gray-200">
        <div class="flex">
          <% tabs.each do |tab| %>
            <button
              @click="switchToTab('<%= tab[:key] %>')"
              class="<%= tab_classes(tab[:key]) %>"
              data-tab="<%= tab[:key] %>"
            >
              <% if tab[:icon] %>
                <%= icon name: tab[:icon], class: "h-4 w-4 mx-auto mb-1" %>
              <% end %>
              <span class="block text-xs"><%= tab[:label] %></span>
            </button>
          <% end %>
        </div>
      </div>
    <% end %>
  </div>

  <!-- Desktop Header -->
  <div class="<%= desktop_header_classes %>">
    <div class="flex items-center justify-between">
      <div class="flex items-center">
        <% if show_back_button %>
          <%= link_to back_url || 'javascript:history.back()',
              class: "mr-4 inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500" do %>
            <%= icon name: "chevron-left", class: "h-4 w-4 mr-2" %>
            Back
          <% end %>
        <% end %>
        <h1 class="text-2xl font-bold text-gray-900"><%= title %></h1>
      </div>

      <!-- Desktop Actions -->
      <% if actions.any? %>
        <div class="flex items-center">
          <% actions.each do |action| %>
            <%= link_to action[:url],
                class: "#{action_button_classes(action)} #{desktop_action_classes}",
                method: action[:method] || :get do %>
              <% if action[:icon] %>
                <%= icon name: action[:icon], class: "h-4 w-4 mr-2" %>
              <% end %>
              <%= action[:label] %>
            <% end %>
          <% end %>
        </div>
      <% end %>
    </div>

    <!-- Desktop Tabs -->
    <% if tabs.any? %>
      <div class="mt-6">
        <div class="border-b border-gray-200">
          <nav class="-mb-px flex space-x-8">
            <% tabs.each do |tab| %>
              <button
                @click="switchToTab('<%= tab[:key] %>')"
                class="<%= tab_classes(tab[:key]) %> py-2 px-1 border-b-2 font-medium text-sm"
                data-tab="<%= tab[:key] %>"
              >
                <% if tab[:icon] %>
                  <%= icon name: tab[:icon], class: "h-4 w-4 mr-2 inline" %>
                <% end %>
                <%= tab[:label] %>
              </button>
            <% end %>
          </nav>
        </div>
      </div>
    <% end %>
  </div>

  <!-- Mobile Sidebar Overlay -->
  <div
    x-show="sidebarOpen"
    @click="closeMobileMenu()"
    x-transition:enter="transition-opacity ease-linear duration-300"
    x-transition:enter-start="opacity-0"
    x-transition:enter-end="opacity-100"
    x-transition:leave="transition-opacity ease-linear duration-300"
    x-transition:leave-start="opacity-100"
    x-transition:leave-end="opacity-0"
    class="fixed inset-0 z-40 bg-gray-600 bg-opacity-75 lg:hidden"
  ></div>

  <!-- Mobile Sidebar -->
  <div
    x-show="sidebarOpen"
    x-transition:enter="transition ease-in-out duration-300 transform"
    x-transition:enter-start="-translate-x-full"
    x-transition:enter-end="translate-x-0"
    x-transition:leave="transition ease-in-out duration-300 transform"
    x-transition:leave-start="translate-x-0"
    x-transition:leave-end="-translate-x-full"
    class="fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-xl lg:hidden"
  >
    <div class="flex items-center justify-between px-4 py-3 border-b border-gray-200">
      <h2 class="text-lg font-semibold text-gray-900">Menu</h2>
      <button
        @click="closeMobileMenu()"
        class="p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100"
      >
        <%= icon name: "times", class: "h-5 w-5" %>
      </button>
    </div>

    <div class="px-4 py-4">
      <!-- Mobile Navigation Content -->
      <%= content_for :mobile_sidebar if content_for?(:mobile_sidebar) %>
    </div>
  </div>

  <!-- Main Content -->
  <main class="flex-1">
    <div class="px-4 py-6 sm:px-6 lg:px-8">
      <%= content %>
    </div>
  </main>

  <!-- Mobile Filters Modal -->
  <div id="mobile-filters-modal" class="fixed inset-0 z-50 hidden lg:hidden">
    <div class="fixed inset-0 bg-gray-600 bg-opacity-75" onclick="mobileReports.hideMobileFilters()"></div>
    <div class="fixed bottom-0 left-0 right-0 bg-white rounded-t-lg shadow-xl max-h-96 overflow-y-auto">
      <div class="flex items-center justify-between px-4 py-3 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">Filters</h3>
        <button
          onclick="mobileReports.hideMobileFilters()"
          class="p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100"
        >
          <%= icon name: "times", class: "h-5 w-5" %>
        </button>
      </div>

      <div class="px-4 py-4">
        <%= content_for :mobile_filters if content_for?(:mobile_filters) %>
      </div>
    </div>
  </div>

  <!-- Mobile Action Sheet -->
  <div id="mobile-action-sheet" class="fixed inset-0 z-50 hidden lg:hidden">
    <div class="fixed inset-0 bg-gray-600 bg-opacity-75" onclick="hideMobileActionSheet()"></div>
    <div class="fixed bottom-0 left-0 right-0 bg-white rounded-t-lg shadow-xl">
      <div class="px-4 py-6">
        <% if actions.any? %>
          <div class="space-y-3">
            <% actions.each do |action| %>
              <%= link_to action[:url],
                  class: "#{action_button_classes(action)} #{mobile_action_classes}",
                  method: action[:method] || :get,
                  onclick: "hideMobileActionSheet()" do %>
                <% if action[:icon] %>
                  <%= icon name: action[:icon], class: "h-4 w-4 mr-2" %>
                <% end %>
                <%= action[:label] %>
              <% end %>
            <% end %>
          </div>
        <% end %>

        <button
          onclick="hideMobileActionSheet()"
          class="w-full mt-4 inline-flex justify-center items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500"
        >
          Cancel
        </button>
      </div>
    </div>
  </div>
</div>

<script>
  function showMobileActionSheet() {
    document.getElementById('mobile-action-sheet').classList.remove('hidden');
  }

  function hideMobileActionSheet() {
    document.getElementById('mobile-action-sheet').classList.add('hidden');
  }

  // Add touch feedback
  document.addEventListener('touchstart', function(e) {
    if (e.target.matches('button, .btn, a[role="button"]')) {
      e.target.style.transform = 'scale(0.95)';
    }
  });

  document.addEventListener('touchend', function(e) {
    if (e.target.matches('button, .btn, a[role="button"]')) {
      setTimeout(() => {
        e.target.style.transform = '';
      }, 100);
    }
  });
</script>
