<% content_for :css do %>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css" />
<% end %>

<%= render Mobile::ResponsiveLayoutComponent.new(
  title: "Reports & Analytics",
  show_back_button: false,
  actions: [
    { label: "Analytics Dashboard", url: analytics_reports_path, icon: "chart-bar", style: "primary" },
    { label: "Real-Time Analytics", url: dashboard_real_time_analytics_path, icon: "wave-pulse", style: "secondary" },
    { label: "Quick Export", url: "#", icon: "download", style: "secondary", onclick: "showMobileActionSheet()" }
  ],
  tabs: [
    { key: "reports", label: "Reports", icon: "file-lines" },
    { key: "builder", label: "Builder", icon: "sliders" },
    { key: "scheduled", label: "Scheduled", icon: "calendar" }
  ],
  current_tab: "reports"
) do %>

  <!-- Tab Content Areas -->
  <div data-tab-content="reports" class="block" style="display: block;">
    <!-- Quick Stats Overview -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
      <%= render Dashboard::StatCardComponent.new(
        title: "Total Reports",
        value: @available_reports.sum { |category| category[:reports].count },
        icon_name: "file-lines",
        color: "blue"
      ) %>

      <%= render Dashboard::StatCardComponent.new(
        title: "This Month",
        value: Sale.where(created_at: Date.current.beginning_of_month..Date.current.end_of_month).count,
        icon_name: "chart-line-up",
        color: "green",
        subtitle: "Sales generated"
      ) %>

      <%= render Dashboard::StatCardComponent.new(
        title: "Export Formats",
        value: "4",
        icon_name: "download",
        color: "purple",
        subtitle: "Excel, CSV, PDF, JSON"
      ) %>

      <%= render Dashboard::StatCardComponent.new(
        title: "Last Export",
        value: "2 hrs ago",
        icon_name: "clock",
        color: "orange",
        subtitle: "Sales report"
      ) %>
    </div>

    <!-- Available Reports -->
    <div class="space-y-8">
      <% @available_reports.each do |category| %>
        <div class="bg-white shadow-sm rounded-lg border border-gray-200">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900"><%= category[:category] %></h3>
            <p class="text-sm text-gray-600 mt-1"><%= category[:reports].count %> reports available</p>
          </div>

          <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <% category[:reports].each do |report| %>
                <div class="border border-gray-200 rounded-lg p-4 hover:border-zeiss-300 hover:shadow-md transition-all duration-200">
                  <div class="flex items-start justify-between">
                    <div class="flex-1">
                      <h4 class="text-sm font-semibold text-gray-900 mb-2"><%= report[:name] %></h4>
                      <p class="text-xs text-gray-600 mb-4"><%= report[:description] %></p>

                      <div class="flex items-center space-x-2">
                        <%= component "modal", id: "#{report[:key]}_dialog" do |modal| %>
                          <% modal.with_button title: "Generate",
                           class: "inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-white bg-zeiss-600 hover:bg-zeiss-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500",
                           click: "modalOpen = true" %>

                          <div class="space-y-4">
                            <div>
                              <h3 class="text-lg font-medium text-gray-900 mb-4">Generate <%= report[:name] %></h3>
                              <p class="text-sm text-gray-600 mb-4"><%= report[:description] %></p>
                            </div>

                            <%
                              # Handle special route cases for different report types
                              report_url = case report[:key]
                              when 'user_performance'
                                user_performance_path(current_user)
                              when 'sales_analytics'
                                sales_analytics_path
                              when 'financial_reports'
                                financial_reports_path
                              when 'real_time_analytics'
                                dashboard_real_time_analytics_path
                              when 'sales'
                                sales_reports_path
                              when 'orders'
                                orders_reports_path
                              when 'login'
                                login_reports_path
                              when 'system_analytics'
                                # System analytics would go to real-time analytics for now
                                dashboard_real_time_analytics_path
                              when 'regional_performance'
                                # Regional performance would go to sales analytics
                                sales_analytics_path
                              when 'brand_analytics'
                                # Brand analytics would go to sales analytics
                                sales_analytics_path
                              else
                                # Fallback to main reports path
                                reports_path
                              end
                            %>
                            <%= form_with url: report_url, local: true, class: "space-y-4" do |form| %>
                              <div>
                                <%= form.label :target, "Date Range", class: "block text-sm font-medium text-gray-700" %>
                                <div class="mt-1">
                                  <%= form.text_field :target,
                                    class: "shadow-sm focus:ring-zeiss-500 focus:border-zeiss-500 block w-full sm:text-sm border-gray-300 rounded-md",
                                    "x-flatpickr": "{altInput: true, dateFormat: 'Z', mode: 'range'}",
                                    required: true,
                                    placeholder: "Select date range" %>
                                </div>
                              </div>

                              <div>
                                <%= form.label :format, "Export Format", class: "block text-sm font-medium text-gray-700" %>
                                <div class="mt-1">
                                  <%= form.select :format,
                                    options_for_select([
                                      ['Excel (.xlsx)', 'xlsx'],
                                      ['CSV (.csv)', 'csv'],
                                      ['PDF (.pdf)', 'pdf']
                                    ], 'xlsx'),
                                    {},
                                    { class: "shadow-sm focus:ring-zeiss-500 focus:border-zeiss-500 block w-full sm:text-sm border-gray-300 rounded-md" } %>
                                </div>
                              </div>

                              <div class="flex justify-end space-x-3 pt-4">
                                <button
                                type="button"
                                @click="modalOpen = false"
                                class="inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500"
                              >
                                  Cancel
                                </button>
                                <%= form.submit "Generate & Download",
                                  class: "inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-zeiss-600 hover:bg-zeiss-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500",
                                  "@click": "modalOpen = false" %>
                              </div>
                            <% end %>
                          </div>
                        <% end %>

                        <% if report[:key] == 'sales_analytics' %>
                          <%= link_to analytics_reports_path,
                            class: "inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500" do %>
                            <%= icon name: "eye", class: "h-3 w-3 mr-1" %>
                            View
                          <% end %>
                        <% end %>
                      </div>
                    </div>

                    <div class="ml-4">
                      <%= icon name: "file-lines", class: "h-8 w-8 text-gray-400" %>
                    </div>
                  </div>
                </div>
              <% end %>
            </div>
          </div>
        </div>
      <% end %>
    </div>
  </div>

  <!-- Builder Tab Content -->
  <div data-tab-content="builder" class="hidden">
    <div class="mb-8">
      <%= render Reports::ReportBuilderComponent.new(user: current_user) %>
    </div>
  </div>

  <!-- Scheduled Tab Content -->
  <div data-tab-content="scheduled" class="hidden">
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="flex items-center justify-between mb-6">
        <h3 class="text-lg font-semibold text-gray-900">Scheduled Reports</h3>
        <button
          onclick="createScheduledReport()"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-zeiss-600 hover:bg-zeiss-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-zeiss-500"
        >
          <%= icon name: "plus", class: "h-4 w-4 mr-2" %>
          New Scheduled Report
        </button>
      </div>

      <!-- Scheduled Reports List -->
      <div class="space-y-4">
        <!-- Example scheduled report -->
        <div class="border border-gray-200 rounded-lg p-4">
          <div class="flex items-center justify-between">
            <div class="flex-1">
              <h4 class="text-sm font-semibold text-gray-900">Monthly Sales Report</h4>
              <p class="text-xs text-gray-600 mt-1">Automatically generated on the 1st of each month</p>
              <div class="flex items-center space-x-4 mt-2">
                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                  Active
                </span>
                <span class="text-xs text-gray-500">Next run: <%= (Date.current.beginning_of_month + 1.month).strftime('%B %d, %Y') %></span>
                <span class="text-xs text-gray-500">Format: Excel</span>
              </div>
            </div>
            <div class="flex items-center space-x-2">
              <button
                onclick="editScheduledReport(1)"
                class="inline-flex items-center px-2 py-1 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50"
              >
                <%= icon name: "pencil", class: "h-3 w-3 mr-1" %>
                Edit
              </button>
              <button
                onclick="deleteScheduledReport(1)"
                class="inline-flex items-center px-2 py-1 border border-gray-300 shadow-sm text-xs font-medium rounded text-red-700 bg-white hover:bg-red-50"
              >
                <%= icon name: "trash", class: "h-3 w-3 mr-1" %>
                Delete
              </button>
            </div>
          </div>
        </div>

        <!-- Example scheduled report 2 -->
        <div class="border border-gray-200 rounded-lg p-4">
          <div class="flex items-center justify-between">
            <div class="flex-1">
              <h4 class="text-sm font-semibold text-gray-900">Weekly User Performance</h4>
              <p class="text-xs text-gray-600 mt-1">Sent every Monday morning to team leads</p>
              <div class="flex items-center space-x-4 mt-2">
                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                  Active
                </span>
                <span class="text-xs text-gray-500">Next run: <%= Date.current.next_occurring(:monday).strftime('%B %d, %Y') %></span>
                <span class="text-xs text-gray-500">Format: PDF</span>
              </div>
            </div>
            <div class="flex items-center space-x-2">
              <button
                onclick="editScheduledReport(2)"
                class="inline-flex items-center px-2 py-1 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50"
              >
                <%= icon name: "pencil", class: "h-3 w-3 mr-1" %>
                Edit
              </button>
              <button
                onclick="deleteScheduledReport(2)"
                class="inline-flex items-center px-2 py-1 border border-gray-300 shadow-sm text-xs font-medium rounded text-red-700 bg-white hover:bg-red-50"
              >
                <%= icon name: "trash", class: "h-3 w-3 mr-1" %>
                Delete
              </button>
            </div>
          </div>
        </div>

        <!-- Empty state when no scheduled reports -->
        <div class="text-center py-8" style="display: none;" id="no-scheduled-reports">
          <%= icon name: "calendar", class: "h-12 w-12 text-gray-400 mx-auto mb-4" %>
          <h3 class="text-lg font-medium text-gray-900 mb-2">No Scheduled Reports</h3>
          <p class="text-sm text-gray-600 mb-4">
            Create automated reports that run on a schedule and get delivered to your email.
          </p>
          <button
            onclick="createScheduledReport()"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-zeiss-600 hover:bg-zeiss-700"
          >
            <%= icon name: "plus", class: "h-4 w-4 mr-2" %>
            Create Your First Scheduled Report
          </button>
        </div>
      </div>
    </div>
  </div>
<% end %>

<script>
  // Simple tab switching function
  function switchToTab(tabName) {
    // Hide all tab content
    document.querySelectorAll('[data-tab-content]').forEach(content => {
      content.classList.add('hidden');
      content.classList.remove('block');
    });

    // Show selected tab content
    const targetContent = document.querySelector(`[data-tab-content="${tabName}"]`);
    if (targetContent) {
      targetContent.classList.remove('hidden');
      targetContent.classList.add('block');
    }

    // Update tab indicators
    document.querySelectorAll('[data-tab]').forEach(tab => {
      tab.classList.remove('border-zeiss-500', 'text-zeiss-600', 'bg-zeiss-50');
      tab.classList.add('text-gray-500', 'hover:text-gray-700');
    });

    const activeTab = document.querySelector(`[data-tab="${tabName}"]`);
    if (activeTab) {
      activeTab.classList.add('border-zeiss-500', 'text-zeiss-600', 'bg-zeiss-50');
      activeTab.classList.remove('text-gray-500', 'hover:text-gray-700');
    }
  }

  // Override Alpine.js tab clicks after page load
  document.addEventListener('DOMContentLoaded', function() {
    // Set up tab click handlers
    document.querySelectorAll('[data-tab]').forEach(tab => {
      tab.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        const tabName = this.getAttribute('data-tab');
        switchToTab(tabName);
      });
    });

    // Initialize the first tab as active
    switchToTab('reports');
  });

  function exportQuickReport(type, period) {
    const endDate = new Date();
    const startDate = new Date();

    switch(period) {
      case '7d':
        startDate.setDate(endDate.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(endDate.getDate() - 30);
        break;
      case '90d':
        startDate.setDate(endDate.getDate() - 90);
        break;
    }

    const dateRange = `${startDate.toISOString().split('T')[0]} to ${endDate.toISOString().split('T')[0]}`;
    const form = document.createElement('form');
    form.method = 'POST';

    // Handle different report types with correct paths
    let actionPath;
    switch(type) {
      case 'sales':
        actionPath = '/sales/reports.xlsx';
        break;
      case 'orders':
        actionPath = '/orders/reports.xlsx';
        break;
      case 'users':
        actionPath = '/login/reports.xlsx';
        break;
      default:
        actionPath = `/${type}/reports.xlsx`;
    }
    form.action = actionPath;

    const targetInput = document.createElement('input');
    targetInput.type = 'hidden';
    targetInput.name = 'target';
    targetInput.value = dateRange;

    form.appendChild(targetInput);
    document.body.appendChild(form);
    form.submit();
    document.body.removeChild(form);
  }

  // Scheduled Reports Functions
  function createScheduledReport() {
    // This would open a modal or redirect to a form for creating scheduled reports
    alert('Create Scheduled Report functionality would be implemented here');
  }

  function editScheduledReport(id) {
    // This would open a modal or redirect to edit the scheduled report
    alert(`Edit Scheduled Report ${id} functionality would be implemented here`);
  }

  function deleteScheduledReport(id) {
    if (confirm('Are you sure you want to delete this scheduled report?')) {
      // This would make an AJAX call to delete the scheduled report
      alert(`Delete Scheduled Report ${id} functionality would be implemented here`);
    }
  }
</script>
